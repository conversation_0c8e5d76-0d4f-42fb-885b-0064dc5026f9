const choices = ["rock", "paper", "scissors"];
let playerScore = 0;
let computerScore = 0;
let round = 1;

const playerScoreEl = document.getElementById("player-score");
const computerScoreEl = document.getElementById("computer-score");
const roundNumberEl = document.getElementById("round-number");
const playerChoiceEl = document.getElementById("player-choice");
const computerChoiceEl = document.getElementById("computer-choice");
const roundResultEl = document.getElementById("round-result");
const resetBtn = document.getElementById("reset-btn");

document.querySelectorAll(".choice-btn").forEach(button => {
  button.addEventListener("click", () => {
    if (round > 5) return;

    const playerChoice = button.dataset.choice;
    const computerChoice = choices[Math.floor(Math.random() * 3)];

    playerChoiceEl.textContent = emoji(playerChoice);
    computerChoiceEl.textContent = emoji(computerChoice);

    const result = getWinner(playerChoice, computerChoice);
    if (result === "player") {
      playerScore++;
      roundResultEl.textContent = "You win this round!";
    } else if (result === "computer") {
      computerScore++;
      roundResultEl.textContent = "Computer wins this round!";
    } else {
      roundResultEl.textContent = "It's a tie!";
    }

    playerScoreEl.textContent = playerScore;
    computerScoreEl.textContent = computerScore;

    round++;
    roundNumberEl.textContent = round <= 5 ? round : 5;

    if (round > 5) {
      showFinalResult();
      resetBtn.style.display = "inline-block";
    }
  });
});

resetBtn.addEventListener("click", () => {
  playerScore = 0;
  computerScore = 0;
  round = 1;
  playerScoreEl.textContent = 0;
  computerScoreEl.textContent = 0;
  roundNumberEl.textContent = 1;
  playerChoiceEl.textContent = "?";
  computerChoiceEl.textContent = "?";
  roundResultEl.textContent = "Make your choice!";
  resetBtn.style.display = "none";
});

function getWinner(player, computer) {
  if (player === computer) return "tie";
  if (
    (player === "rock" && computer === "scissors") ||
    (player === "paper" && computer === "rock") ||
    (player === "scissors" && computer === "paper")
  ) return "player";
  return "computer";
}

function showFinalResult() {
  if (playerScore > computerScore) {
    roundResultEl.textContent = "🎉 Congratulations! You Won The Game!";
  } else if (computerScore > playerScore) {
    roundResultEl.textContent = "💻 Game Over! Computer Wins The Game!";
  } else {
    roundResultEl.textContent = "🤝 It's a Tie Game! Try Again!";
  }
}

function emoji(choice) {
  return choice === "rock" ? "🪨" : choice === "paper" ? "📄" : "✂️";
}
