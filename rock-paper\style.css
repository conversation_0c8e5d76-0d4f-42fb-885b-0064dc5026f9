* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: white;
}

.game-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  background: linear-gradient(45deg, #ffffff, #a8edea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.scoreboard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.score-section {
  flex: 1;
  text-align: center;
}

.score-label {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
  margin-bottom: 8px;
}

.score-value {
  font-size: 3rem;
  font-weight: 700;
  color: #a8edea;
}

.vs-indicator {
  background: #ff6b6b;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin: 0 20px;
}

.vs-indicator.small {
  padding: 6px 12px;
  font-size: 0.8rem;
  margin: 0 15px;
}

.round-info {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 30px;
  opacity: 0.9;
  color: #a8edea;
}

.choices {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;
  justify-content: center;
}

.choice-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  color: white;
  min-width: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.choice-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.choice-btn:active {
  transform: translateY(0);
}

.choice-icon {
  font-size: 2rem;
}

.choice-label {
  font-size: 0.9rem;
  font-weight: 500;
}

.results {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.result-section {
  flex: 1;
  text-align: center;
}

.result-label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.8;
  margin-bottom: 8px;
}

.result-choice {
  font-size: 2.5rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-message {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 30px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.reset-btn {
  background: linear-gradient(45deg, #ff9a56, #ff6b6b);
  border: none;
  border-radius: 12px;
  padding: 15px 30px;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.reset-btn:active {
  transform: translateY(0);
}

@media (max-width: 600px) {
  .game-container {
    padding: 30px 20px;
  }

  .game-title {
    font-size: 2rem;
  }

  .choices {
    flex-direction: column;
    align-items: center;
  }

  .choice-btn {
    width: 200px;
  }

  .scoreboard, .results {
    flex-direction: column;
    gap: 15px;
  }

  .vs-indicator {
    margin: 0;
  }
}
